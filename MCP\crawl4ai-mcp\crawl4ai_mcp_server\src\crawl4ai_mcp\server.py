"""
Crawl4AI MCP Server - Standalone MCP server for direct AI controller integration.

This server provides Model Control Protocol (MCP) access to Crawl4AI functionality
without requiring Docker or API dependencies. It uses stdio transport for direct
communication with AI controllers.
"""

import asyncio
import logging
from typing import Dict, Any, List

from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
import mcp.server.stdio

from .config import config
from .tools import (
    MarkdownTool,
    HTMLTool,
    CrawlTool,
    ScreenshotTool,
    PDFTool,
    JavaScriptTool,
    ContextTool
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create the MCP server
server = Server(config.name)

# Tool registry
TOOLS = {
    "md": MarkdownTool,
    "html": HTMLTool,
    "crawl": CrawlTool,
    "screenshot": ScreenshotTool,
    "pdf": PDFTool,
    "execute_js": JavaScriptTool,
    "ask": ContextTool
}


@server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    """List available tools."""
    tools = []
    for tool_name, tool_class in TOOLS.items():
        try:
            tool_schema = tool_class.get_schema()
            tools.append(tool_schema)
        except Exception as e:
            logger.error(f"Error getting schema for tool {tool_name}: {e}")
    
    return tools


@server.call_tool()
async def handle_call_tool(
    name: str, arguments: Dict[str, Any] | None
) -> List[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """Handle tool execution requests."""
    
    if name not in TOOLS:
        raise ValueError(f"Unknown tool: {name}")
    
    tool_class = TOOLS[name]
    
    try:
        # Execute the tool with provided arguments
        result = await tool_class.execute(arguments or {})
        return result
        
    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        # Return error as text content
        error_response = {
            "error": 500,
            "detail": str(e),
            "success": False
        }
        import json
        return [types.TextContent(
            type="text",
            text=json.dumps(error_response)
        )]


@server.list_resources()
async def handle_list_resources() -> List[types.Resource]:
    """List available resources."""
    # For now, we don't expose any static resources
    # This could be extended to provide access to documentation, schemas, etc.
    return []


@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Read a specific resource."""
    # Not implemented for now
    raise ValueError(f"Resource not found: {uri}")


@server.list_prompts()
async def handle_list_prompts() -> List[types.Prompt]:
    """List available prompts."""
    # Could be extended to provide pre-defined prompts for common crawling tasks
    return []


@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: Dict[str, str] | None
) -> types.GetPromptResult:
    """Get a specific prompt."""
    # Not implemented for now
    raise ValueError(f"Unknown prompt: {name}")


async def main():
    """Main entry point for the MCP server."""
    logger.info(f"Starting Crawl4AI MCP Server v{config.version}")
    logger.info(f"Available tools: {list(TOOLS.keys())}")
    
    # Run the server using stdin/stdout streams
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name=config.name,
                server_version=config.version,
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


if __name__ == "__main__":
    asyncio.run(main())