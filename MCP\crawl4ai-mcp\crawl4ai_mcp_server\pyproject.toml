[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "crawl4ai-mcp-server"
version = "0.1.0"
description = "Standalone MCP server for Crawl4AI - direct AI controller integration without <PERSON><PERSON>"
authors = [
    {name = "Crawl4AI Team"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "mcp>=1.0.0",
    "crawl4ai>=0.6.2",
    "pydantic>=2.10",
    "aiofiles>=24.1.0",
    "rank-bm25>=0.2.2",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio",
]

[project.scripts]
crawl4ai-mcp = "crawl4ai_mcp.server:main"

[tool.hatch.build.targets.wheel]
packages = ["src/crawl4ai_mcp"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
]