"""Utility functions for Crawl4AI MCP Server."""

import json
import base64
from typing import Any, Dict, List, Optional
from urllib.parse import unquote

import mcp.types as types


def create_text_content(text: str) -> types.TextContent:
    """Create MCP TextContent from string."""
    return types.TextContent(type="text", text=text)


def create_json_response(data: Any) -> List[types.TextContent]:
    """Create MCP response with JSON data."""
    return [create_text_content(json.dumps(data, default=str))]


def create_error_response(error: str, status_code: int = 500) -> List[types.TextContent]:
    """Create MCP error response."""
    error_data = {
        "error": status_code,
        "detail": error,
        "success": False
    }
    return [create_text_content(json.dumps(error_data))]


def validate_url(url: str) -> str:
    """Validate and normalize URL."""
    decoded_url = unquote(url)
    if not decoded_url.startswith(('http://', 'https://')):
        decoded_url = 'https://' + decoded_url
    return decoded_url


def encode_binary_to_base64(data: bytes) -> str:
    """Encode binary data to base64 string."""
    return base64.b64encode(data).decode('utf-8')


def create_tool_schema(properties: Dict[str, Any], required: List[str]) -> Dict[str, Any]:
    """Create JSON schema for MCP tool."""
    return {
        "type": "object",
        "properties": properties,
        "required": required
    }