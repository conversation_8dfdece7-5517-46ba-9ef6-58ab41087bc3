# Crawl4AI MCP服务器项目概述

## 项目用途
这是一个基于Crawl4AI的MCP（Model Context Protocol）服务器，提供直接的AI控制器集成，无需Docker或API依赖。它使用stdio传输与AI控制器进行直接通信。

## 技术栈
- **Python 3.9+**
- **MCP (Model Context Protocol) >= 1.0.0** - 用于AI工具集成
- **Crawl4AI >= 0.6.2** - 核心网页爬取功能
- **Pydantic >= 2.10** - 数据验证和配置管理
- **AsyncIO** - 异步编程架构
- **Playwright** - 浏览器自动化（通过Crawl4AI）

## 项目结构
```
crawl4ai_mcp_server/
├── src/crawl4ai_mcp/
│   ├── server.py           # 主要MCP服务器实现
│   ├── config.py           # 配置管理
│   ├── browser_utils.py    # 浏览器配置工具
│   ├── utils.py           # 通用工具函数
│   └── tools/             # 工具实现目录
│       ├── crawl.py       # 爬取工具
│       ├── screenshot.py  # 截图工具
│       ├── html.py        # HTML提取工具
│       ├── markdown.py    # Markdown生成工具
│       ├── pdf.py         # PDF处理工具
│       ├── javascript.py  # JavaScript执行工具
│       └── context.py     # 上下文查询工具
├── pyproject.toml         # 项目配置和依赖
└── README.md             # 项目文档
```

## 核心架构
- **MCP服务器架构**：使用stdin/stdout通信协议
- **工具注册系统**：动态工具发现和执行
- **浏览器管理**：基于Edge/Chromium的用户数据目录
- **异步处理**：全异步工具执行

## 当前问题
浏览器生命周期管理存在问题：每次工具调用都创建新的AsyncWebCrawler实例，导致浏览器反复启动和关闭，影响性能和稳定性。