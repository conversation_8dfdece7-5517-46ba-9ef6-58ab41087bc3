"""HTML extraction tool for Crawl4AI MCP Server."""

from typing import Dict, Any, List

import mcp.types as types
from crawl4ai import Async<PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserConfig, CrawlerRunConfig
from crawl4ai.utils import preprocess_html_for_schema

from ..utils import create_json_response, create_error_response, validate_url, create_tool_schema
from ..browser_utils import create_browser_config


class HTMLTool:
    """Tool for extracting preprocessed HTML from web pages."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="html",
            description="Crawls the URL, preprocesses the raw HTML for schema extraction, and returns the processed HTML. Use when you need sanitized HTML structures for building schemas or further processing.",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to fetch"
                    }
                },
                required=["url"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the HTML extraction tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)
            
            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)
            
            # Extract HTML
            processed_html = await HTMLTool._extract_html(validated_url)
            
            # Return response
            response_data = {
                "html": processed_html,
                "url": validated_url,
                "success": True
            }
            
            return create_json_response(response_data)
            
        except Exception as e:
            return create_error_response(f"HTML extraction failed: {str(e)}")
    
    @staticmethod
    async def _extract_html(url: str) -> str:
        """Extract and preprocess HTML from URL."""
        cfg = CrawlerRunConfig()
        
        # Create browser config with Edge settings
        browser_cfg = create_browser_config()
        
        async with AsyncWebCrawler(config=browser_cfg) as crawler:
            results = await crawler.arun(url=url, config=cfg)
        
        if not results or not results[0].success:
            error_msg = results[0].error_message if results else "Crawl failed"
            raise Exception(error_msg)
        
        raw_html = results[0].html
        processed_html = preprocess_html_for_schema(raw_html)
        return processed_html