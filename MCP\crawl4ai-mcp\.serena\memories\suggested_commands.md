# Windows系统建议命令

## 开发环境设置
```cmd
# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -e .
pip install -e ".[dev]"
```

## 运行和测试
```cmd
# 启动MCP服务器
python -m crawl4ai_mcp.server

# 或者使用入口点
crawl4ai-mcp

# 运行测试
pytest tests/
pytest tests/ -v
```

## 浏览器相关命令
```cmd
# 安装Playwright浏览器
python -m playwright install chromium
python -m playwright install --with-deps chromium

# 检查浏览器安装
python -m playwright show-trace
```

## 开发工具命令
```cmd
# 代码格式化
black src/
python -m black src/

# 类型检查
mypy src/

# 代码检查
flake8 src/
```

## 文件操作命令（Windows）
```cmd
# 目录操作
dir                    # 列出目录内容
cd <directory>         # 切换目录
mkdir <directory>      # 创建目录
rmdir /s <directory>   # 删除目录

# 文件操作
type <file>           # 查看文件内容
copy <source> <dest>  # 复制文件
del <file>           # 删除文件

# 进程管理
tasklist              # 列出进程
taskkill /f /pid <pid> # 强制终止进程
netstat -an | findstr 9222  # 查看端口占用
```

## Git命令
```cmd
git status
git add .
git commit -m "message"
git push origin main
git pull origin main
```

## 项目特定命令
```cmd
# 清理缓存和临时文件
rmdir /s "%USERPROFILE%\.crawl4ai" 2>nul
rmdir /s "temp_profiles" 2>nul

# 检查Edge浏览器路径
dir "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"

# 检查Chrome浏览器路径  
dir "C:\Program Files\Google\Chrome\Application\chrome.exe"
```