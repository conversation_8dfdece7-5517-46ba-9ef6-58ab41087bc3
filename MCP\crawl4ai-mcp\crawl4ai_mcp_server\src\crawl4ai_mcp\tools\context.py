"""Library context tool for Crawl4AI MCP Server."""

import os
import re
from typing import Dict, Any, List, Optional

import mcp.types as types
from rank_bm25 import BM<PERSON><PERSON><PERSON><PERSON>

from ..utils import create_json_response, create_error_response, create_tool_schema


class ContextTool:
    """Tool for querying Crawl4AI library context and documentation."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="ask",
            description="Query the Crawl4AI library context for decision making or code generation tasks. Returns extensive information about Crawl4AI functionality.",
            inputSchema=create_tool_schema(
                properties={
                    "context_type": {
                        "type": "string",
                        "enum": ["code", "doc", "all"],
                        "default": "all",
                        "description": "Specify 'code' for code context, 'doc' for documentation context, or 'all' for both"
                    },
                    "query": {
                        "type": "string",
                        "description": "RECOMMENDED search query to filter paragraphs using BM25. Leave empty to get all context."
                    },
                    "score_ratio": {
                        "type": "number",
                        "minimum": 0.0,
                        "maximum": 1.0,
                        "default": 0.5,
                        "description": "Minimum score as a fraction of the maximum score for filtering results"
                    },
                    "max_results": {
                        "type": "integer",
                        "minimum": 1,
                        "default": 20,
                        "description": "Maximum number of results to return"
                    }
                },
                required=[]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the context query tool."""
        try:
            # Extract arguments with defaults
            context_type = arguments.get("context_type", "all")
            query = arguments.get("query")
            score_ratio = arguments.get("score_ratio", 0.5)
            max_results = arguments.get("max_results", 20)
            
            # Get context
            result = await ContextTool._get_context(
                context_type, query, score_ratio, max_results
            )
            
            return create_json_response(result)
            
        except Exception as e:
            return create_error_response(f"Context query failed: {str(e)}")
    
    @staticmethod
    async def _get_context(
        context_type: str,
        query: Optional[str] = None,
        score_ratio: float = 0.5,
        max_results: int = 20
    ) -> Dict[str, Any]:
        """Get Crawl4AI library context."""
        
        # Load context files (these would need to be created or copied from the Docker deployment)
        code_content = ContextTool._load_code_context()
        doc_content = ContextTool._load_doc_context()
        
        # If no query, return raw contexts
        if not query:
            result = {}
            if context_type in ("code", "all"):
                result["code_context"] = code_content
            if context_type in ("doc", "all"):
                result["doc_context"] = doc_content
            return result
        
        # Use BM25 for filtering (simplified implementation)
        # Note: For production, you'd want to use the actual BM25 implementation
        # from the original code or a proper BM25 library
        results = {}
        
        if context_type in ("code", "all"):
            code_results = ContextTool._search_content(
                code_content, query, score_ratio, max_results, "code"
            )
            results["code_results"] = code_results
        
        if context_type in ("doc", "all"):
            doc_results = ContextTool._search_content(
                doc_content, query, score_ratio, max_results, "doc"
            )
            results["doc_results"] = doc_results
        
        return results
    
    @staticmethod
    def _load_code_context() -> str:
        """Load code context. In production, this should be the actual context file."""
        return """# Crawl4AI Code Context
        
This is a placeholder for the actual Crawl4AI code context.
In a production implementation, this would contain:
- Function signatures and documentation
- Class definitions and methods
- Usage examples
- API reference

Key components:
- AsyncWebCrawler: Main crawler class
- CrawlerRunConfig: Configuration for crawl operations
- BrowserConfig: Browser configuration
- Various strategies for content filtering, markdown generation, etc.

For the actual context, copy the c4ai-code-context.md file from the Docker deployment.
"""
    
    @staticmethod
    def _load_doc_context() -> str:
        """Load documentation context. In production, this should be the actual context file."""
        return """# Crawl4AI Documentation Context
        
This is a placeholder for the actual Crawl4AI documentation context.
In a production implementation, this would contain:
- Installation instructions
- Usage guides
- Configuration options
- Examples and tutorials
- Best practices

Key topics:
- Basic crawling operations
- Advanced configuration
- Content filtering strategies
- Markdown generation
- Browser management
- Caching and performance

For the actual context, copy the c4ai-doc-context.md file from the Docker deployment.
"""
    
    @staticmethod
    def _search_content(
        content: str,
        query: str,
        score_ratio: float,
        max_results: int,
        content_type: str
    ) -> List[Dict[str, Any]]:
        """BM25-based content search implementation."""
        
        # Split content into chunks
        if content_type == "code":
            chunks = ContextTool._chunk_code_functions(content)
        else:
            chunks = ContextTool._chunk_doc_sections(content)
        
        if not chunks:
            return []
        
        # Prepare BM25 corpus
        tokenized_chunks = [chunk.split() for chunk in chunks]
        bm25 = BM25Okapi(tokenized_chunks)
        
        # Get BM25 scores
        query_tokens = query.split()
        scores = bm25.get_scores(query_tokens)
        
        # Apply score filtering
        if len(scores) > 0:
            max_score = float(max(scores))
            cutoff = max_score * score_ratio
            
            # Create scored results
            scored_chunks = []
            for i, (chunk, score) in enumerate(zip(chunks, scores)):
                if score >= cutoff:
                    scored_chunks.append({"text": chunk, "score": float(score)})
            
            # Sort by score and limit results
            scored_chunks.sort(key=lambda x: x["score"], reverse=True)
            return scored_chunks[:max_results]
        
        return []
    
    @staticmethod
    def _chunk_code_functions(content: str) -> List[str]:
        """Split code content into function/class chunks."""
        # Simple implementation - split by function/class definitions
        chunks = []
        lines = content.split('\n')
        current_chunk = []
        
        for line in lines:
            if re.match(r'^(def |class |async def )', line.strip()):
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
            else:
                current_chunk.append(line)
        
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    @staticmethod
    def _chunk_doc_sections(content: str) -> List[str]:
        """Split documentation into sections."""
        # Simple implementation - split by markdown headers
        chunks = []
        lines = content.split('\n')
        current_chunk = []
        
        for line in lines:
            if re.match(r'^#+\s', line.strip()):
                if current_chunk:
                    chunks.append('\n'.join(current_chunk))
                current_chunk = [line]
            else:
                current_chunk.append(line)
        
        if current_chunk:
            chunks.append('\n'.join(current_chunk))
        
        return [chunk for chunk in chunks if chunk.strip()]