"""
公文脱敏助手 - PyQt6-Fluent-Widgets 响应式布局版本
"""
import os
import json
import sys
from typing import Dict, List

from PyQt6.QtCore import Qt, pyqtSignal, QMimeData, QUrl
from PyQt6.QtGui import QAction, QKeySequence, QDragEnterEvent, QDropEvent
from PyQt6.QtWidgets import QApplication, QVBoxLayout, QHBoxLayout, QWidget, QFileDialog, QSizePolicy

from qfluentwidgets import (
    FluentWindow, FluentIcon, InfoBar, InfoBarPosition,
    CardWidget, PrimaryPushButton, PushButton, LineEdit, TextEdit,
    RadioButton, FlowLayout, VBoxLayout, BodyLabel, SubtitleLabel,
    MessageBox, TeachingTip, SwitchButton, HorizontalSeparator,
    StateToolTip, setTheme, Theme, isDarkTheme
)

from docx import Document


class LabelManageCard(CardWidget):
    """标签管理卡片"""
    labelChanged = pyqtSignal(str)  # 当前标签改变信号
    labelsUpdated = pyqtSignal(dict)  # 标签列表更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(180)
        
        # 预设标签
        self.preset_labels = ["姓名", "身份证", "电话", "地址", "日期"]
        self.counters = {label: 0 for label in self.preset_labels}
        self.current_label = self.preset_labels[0]
        self.radio_buttons: Dict[str, RadioButton] = {}
        
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        layout = VBoxLayout(self)
        layout.setSpacing(12)
        
        # 标题
        title = SubtitleLabel("标签管理")
        layout.addWidget(title)
        
        # 标签选择区域
        self.label_flow_widget = QWidget()
        self.label_flow_layout = FlowLayout(self.label_flow_widget)
        self.label_flow_layout.setSpacing(8)
        
        # 初始化预设标签
        for label in self.preset_labels:
            self._add_label_radio(label)
        
        layout.addWidget(self.label_flow_widget)
        
        # 自定义标签输入区
        custom_layout = QVBoxLayout()
        
        input_layout = QHBoxLayout()
        self.custom_input = LineEdit()
        self.custom_input.setPlaceholderText("输入自定义标签名称")
        
        self.add_btn = PushButton("添加", self)
        self.add_btn.setIcon(FluentIcon.ADD)
        self.add_btn.setFixedWidth(80)  # 增加按钮宽度
        
        input_layout.addWidget(self.custom_input)
        input_layout.addWidget(self.add_btn)
        
        self.delete_btn = PushButton("删除当前标签", self)
        self.delete_btn.setIcon(FluentIcon.DELETE)
        
        custom_layout.addWidget(BodyLabel("自定义标签:"))
        custom_layout.addLayout(input_layout)
        custom_layout.addWidget(self.delete_btn)
        
        layout.addLayout(custom_layout)
        
    def _connect_signals(self):
        self.custom_input.returnPressed.connect(self._add_custom_label)
        self.add_btn.clicked.connect(self._add_custom_label)
        self.delete_btn.clicked.connect(self._delete_current_label)
        
    def _add_label_radio(self, label: str):
        """添加标签单选按钮"""
        radio = RadioButton(label, self)
        if label == self.current_label:
            radio.setChecked(True)
        
        radio.clicked.connect(lambda: self._on_label_selected(label))
        self.radio_buttons[label] = radio
        self.label_flow_layout.addWidget(radio)
        
    def _on_label_selected(self, label: str):
        """标签选择事件"""
        self.current_label = label
        self.labelChanged.emit(label)
        
    def _add_custom_label(self):
        """添加自定义标签"""
        new_label = self.custom_input.text().strip()
        if not new_label:
            return
            
        if new_label in self.counters:
            InfoBar.warning(
                title="标签已存在",
                content=f"标签 '{new_label}' 已经存在",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            return
            
        self.counters[new_label] = 0
        self._add_label_radio(new_label)
        self.current_label = new_label
        
        # 更新选中状态
        for label, radio in self.radio_buttons.items():
            radio.setChecked(label == new_label)
            
        self.custom_input.clear()
        self.labelChanged.emit(new_label)
        self.labelsUpdated.emit(self.counters.copy())
        
        InfoBar.success(
            title="标签已添加",
            content=f"成功添加标签 '{new_label}'",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self
        )
        
    def _delete_current_label(self):
        """删除当前标签"""
        if self.current_label in self.preset_labels:
            InfoBar.warning(
                title="无法删除",
                content="预设标签无法删除",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            return
            
        # 移除单选按钮
        if self.current_label in self.radio_buttons:
            radio = self.radio_buttons.pop(self.current_label)
            self.label_flow_layout.removeWidget(radio)
            radio.deleteLater()
            
        # 移除计数器
        self.counters.pop(self.current_label, None)
        
        # 切换到第一个预设标签
        self.current_label = self.preset_labels[0]
        self.radio_buttons[self.current_label].setChecked(True)
        
        self.labelChanged.emit(self.current_label)
        self.labelsUpdated.emit(self.counters.copy())
        
    def get_current_label(self) -> str:
        """获取当前选中的标签"""
        return self.current_label
        
    def increment_counter(self, label: str):
        """增加标签计数器"""
        if label in self.counters:
            self.counters[label] += 1
            
    def get_counters(self) -> Dict[str, int]:
        """获取计数器字典"""
        return self.counters.copy()
        
    def set_counters(self, counters: Dict[str, int]):
        """设置计数器字典"""
        self.counters = counters


class FileDropCard(CardWidget):
    """文件拖放卡片"""
    fileDropped = pyqtSignal(str)  # 文件拖放信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(120)
        self.setAcceptDrops(True)
        
        self._init_ui()
        
    def _init_ui(self):
        layout = VBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.label = SubtitleLabel("📄 拖拽文件到这里")
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.hint_label = BodyLabel("支持 .txt 或 .docx 文件")
        self.hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.click_label = BodyLabel("或点击选择文件")
        self.click_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        layout.addWidget(self.label)
        layout.addWidget(self.hint_label)
        layout.addWidget(self.click_label)
        
        # 点击选择文件
        self.mousePressEvent = self._on_click
        
    def _on_click(self, event):
        """点击选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择文件", "", "支持的文件 (*.txt *.docx)")
        if file_path:
            self.fileDropped.emit(file_path)
            
    def dragEnterEvent(self, event: QDragEnterEvent):
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1 and urls[0].isLocalFile():
                file_path = urls[0].toLocalFile()
                ext = os.path.splitext(file_path)[1].lower()
                if ext in ('.txt', '.docx'):
                    event.acceptProposedAction()
                    self.setStyleSheet("CardWidget { border: 2px dashed #0078d4; }")
                    return
        event.ignore()
        
    def dragLeaveEvent(self, event):
        self.setStyleSheet("")
        
    def dropEvent(self, event: QDropEvent):
        self.setStyleSheet("")
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            self.fileDropped.emit(file_path)
            

class OptionsCard(CardWidget):
    """选项卡片"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(100)
        
        self.bulk_replace = False
        self._init_ui()
        
    def _init_ui(self):
        layout = VBoxLayout(self)
        layout.setSpacing(12)
        
        # 标题
        title = SubtitleLabel("选项设置")
        layout.addWidget(title)
        
        # 全文替换选项
        option_layout = QHBoxLayout()
        self.bulk_switch = SwitchButton()
        self.bulk_switch.checkedChanged.connect(self._on_bulk_changed)
        
        option_layout.addWidget(BodyLabel("全文替换模式"))
        option_layout.addStretch()
        option_layout.addWidget(self.bulk_switch)
        
        layout.addLayout(option_layout)
        
        # 快捷键提示
        hint_label = BodyLabel("快捷键: Ctrl+Enter 替换选中文本")
        hint_label.setStyleSheet("color: gray; font-size: 12px;")
        layout.addWidget(hint_label)
        
    def _on_bulk_changed(self, checked: bool):
        """全文替换模式改变"""
        self.bulk_replace = checked
        
    def is_bulk_replace(self) -> bool:
        """是否开启全文替换"""
        return self.bulk_replace


class ActionButtonsCard(CardWidget):
    """操作按钮卡片"""
    saveClicked = pyqtSignal()
    restoreClicked = pyqtSignal()
    helpClicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(120)
        self._init_ui()
        
    def _init_ui(self):
        layout = VBoxLayout(self)
        layout.setSpacing(12)
        
        # 标题
        title = SubtitleLabel("操作")
        layout.addWidget(title)
        
        # 保存按钮
        self.save_btn = PrimaryPushButton("保存脱敏文件")
        self.save_btn.setIcon(FluentIcon.SAVE)
        self.save_btn.clicked.connect(self.saveClicked.emit)
        
        # 恢复按钮
        self.restore_btn = PushButton("恢复原文")
        self.restore_btn.setIcon(FluentIcon.SYNC)
        self.restore_btn.clicked.connect(self.restoreClicked.emit)
        
        # 帮助按钮
        self.help_btn = PushButton("帮助")
        self.help_btn.setIcon(FluentIcon.HELP)
        self.help_btn.clicked.connect(self.helpClicked.emit)
        
        layout.addWidget(self.save_btn)
        layout.addWidget(self.restore_btn)
        layout.addWidget(self.help_btn)


class TextEditWidget(QWidget):
    """文本编辑组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self._init_ui()
        
    def _init_ui(self):
        layout = VBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)
        
        # 标题
        title = SubtitleLabel("文档内容")
        title.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        layout.addWidget(title)
        
        # 文本编辑器
        self.text_edit = TextEdit()
        self.text_edit.setPlaceholderText("文档内容将在这里显示...")
        self.text_edit.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.text_edit.setMinimumHeight(680)  # 设置最小高度
        layout.addWidget(self.text_edit, 1)  # 添加权重，让文本编辑器占据剩余空间
        
    def get_text_edit(self) -> TextEdit:
        """获取文本编辑器"""
        return self.text_edit


class MaskToolInterface(QWidget):
    """公文脱敏助手主界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.file_path = None
        self.replacement_map = {}  # 保存原始词汇的映射关系 {placeholder: original_text}
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        self._init_ui()
        self._init_connections()
        self._setup_shortcuts()
        
    def _init_ui(self):
        """初始化界面"""
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(24, 24, 24, 24)
        
        # 确保主窗口可以扩展
        self.setMinimumSize(1000, 700)
        
        # 左侧控制面板
        left_panel = QWidget()
        left_panel.setFixedWidth(350)
        left_panel.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)
        left_layout = VBoxLayout(left_panel)
        left_layout.setSpacing(16)
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # 应用标题
        title = SubtitleLabel("🛡️ 公文脱敏助手")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        left_layout.addWidget(title)
        
        # 标签管理卡片
        self.label_card = LabelManageCard()
        left_layout.addWidget(self.label_card)
        
        # 选项卡片
        self.options_card = OptionsCard()
        left_layout.addWidget(self.options_card)
        
        # 文件拖放卡片
        self.file_card = FileDropCard()
        left_layout.addWidget(self.file_card)
        
        # 操作按钮卡片
        self.action_card = ActionButtonsCard()
        left_layout.addWidget(self.action_card)
        
        # 添加弹性空间
        left_layout.addStretch()
        
        # 右侧文本编辑区域
        self.text_widget = TextEditWidget()
        self.text_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # 添加到主布局 - 确保布局权重正确
        main_layout.addWidget(left_panel, 0)  # 左侧面板，不拉伸
        main_layout.addWidget(self.text_widget, 1)  # 右侧文本区域，占据剩余空间
        
        # 确保主布局能够充分利用空间
        main_layout.setStretchFactor(self.text_widget, 1)
        
    def _init_connections(self):
        """初始化信号连接"""
        self.file_card.fileDropped.connect(self._on_file_dropped)
        self.action_card.saveClicked.connect(self._save_file)
        self.action_card.restoreClicked.connect(self._restore_dialog)
        self.action_card.helpClicked.connect(self._show_help)
        
    def _setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+Enter 替换选中文本
        replace_action = QAction(self)
        replace_action.setShortcut(QKeySequence("Ctrl+Return"))
        replace_action.triggered.connect(self._replace_selection)
        self.addAction(replace_action)
        
    def _on_file_dropped(self, file_path: str):
        """文件拖放处理"""
        ext = os.path.splitext(file_path)[1].lower()
        if ext not in ('.txt', '.docx'):
            InfoBar.error(
                title="格式错误",
                content="仅支持 .txt 或 .docx 文件",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            return
            
        self.file_path = file_path
        content = self._read_content(file_path, ext)
        self.text_widget.get_text_edit().setPlainText(content)
        
        InfoBar.success(
            title="文件加载成功",
            content=f"已加载文件: {os.path.basename(file_path)}",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self
        )
        
    def _replace_selection(self):
        """替换选中文本"""
        text_edit = self.text_widget.get_text_edit()
        cursor = text_edit.textCursor()
        
        if not cursor.hasSelection():
            return
            
        selected_text = cursor.selectedText()
        if not selected_text.strip():
            return
            
        current_label = self.label_card.get_current_label()
        self.label_card.increment_counter(current_label)
        
        counters = self.label_card.get_counters()
        placeholder = f"{current_label}_{counters[current_label]}"
        
        # 保存原始文本的映射关系
        self.replacement_map[placeholder] = selected_text
        
        if self.options_card.is_bulk_replace():
            # 全文替换
            full_text = text_edit.toPlainText()
            occurrences = full_text.count(selected_text)
            
            # 为每个出现的文本创建唯一的占位符
            new_text = full_text
            for i in range(occurrences):
                current_counters = self.label_card.get_counters()
                current_placeholder = f"{current_label}_{current_counters[current_label] + i}"
                self.replacement_map[current_placeholder] = selected_text
                if i > 0:  # 第一个已经通过increment_counter处理了
                    self.label_card.increment_counter(current_label)
                    
            # 逐个替换，确保每个都有唯一标识
            for i in range(occurrences):
                current_counters = self.label_card.get_counters()
                unique_placeholder = f"{current_label}_{current_counters[current_label] - occurrences + i + 1}"
                new_text = new_text.replace(selected_text, unique_placeholder, 1)
                
            text_edit.setPlainText(new_text)
        else:
            # 仅替换选中部分
            cursor.insertText(placeholder)
            
        InfoBar.info(
            title="替换完成",
            content=f"已将 '{selected_text}' 替换为: {placeholder}",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=1500,
            parent=self
        )
        
    def _save_file(self):
        """保存脱敏文件"""
        if not self.file_path:
            InfoBar.warning(
                title="请先选择文件",
                content="请先拖入或选择要处理的文件",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            return
            
        text_content = self.text_widget.get_text_edit().toPlainText()
        base, ext = os.path.splitext(self.file_path)
        
        masked_path = f"{base}_masked{ext}"
        map_path = f"{base}_map.json"
        
        # 保存脱敏文件
        self._write_content(masked_path, text_content, ext)
        
        # 保存完整映射表（包含原始文本）
        full_mapping = {
            'counters': self.label_card.get_counters(),
            'replacements': self.replacement_map  # 包含 placeholder -> original_text 的映射
        }
        with open(map_path, 'w', encoding='utf-8') as f:
            json.dump(full_mapping, f, ensure_ascii=False, indent=2)
            
        InfoBar.success(
            title="保存成功",
            content=f"已保存:\n{os.path.basename(masked_path)}\n{os.path.basename(map_path)}",
            orient=Qt.Orientation.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=4000,
            parent=self
        )
        
    def _restore_dialog(self):
        """恢复文件对话框"""
        masked_file, _ = QFileDialog.getOpenFileName(
            self, "选择脱敏后的文件", "", "支持的文件 (*.txt *.docx)")
        if not masked_file:
            return
            
        base, ext = os.path.splitext(masked_file)
        default_map = f"{base.rsplit('_masked', 1)[0]}_map.json"
        
        if os.path.exists(default_map):
            map_file = default_map
        else:
            map_file, _ = QFileDialog.getOpenFileName(
                self, "选择映射表文件", "", "JSON文件 (*.json)")
            if not map_file:
                return
                
        restored_path = f"{base.rsplit('_masked', 1)[0]}_restored{ext}"
        
        try:
            self._restore_from_map(masked_file, map_file, restored_path)
            InfoBar.success(
                title="恢复完成",
                content=f"已生成恢复文件:\n{os.path.basename(restored_path)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=4000,
                parent=self
            )
        except Exception as e:
            InfoBar.error(
                title="恢复失败",
                content=f"文件恢复出错: {str(e)}",
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self
            )
            
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
        🛡️ 公文脱敏助手使用说明：
        
        1. 📁 文件导入：拖拽或点击选择 .txt/.docx 文件
        2. 🏷️ 标签管理：选择或自定义敏感信息标签
        3. ✏️ 文本替换：
           • 选中敏感文本
           • 按 Ctrl+Enter 进行替换
           • 开启"全文替换"可替换所有相同文本
        4. 💾 保存文件：保存脱敏文件和映射表
        5. 🔄 恢复功能：使用映射表恢复原始内容
        
        ⌨️ 快捷键：
        • Ctrl+Enter：替换选中文本
        
        💡 界面说明：
        • 左侧：控制面板和操作区域
        • 右侧：文档内容编辑区域
        """
        
        message_box = MessageBox("使用帮助", help_text, self)
        message_box.exec()
        
    @staticmethod
    def _read_content(file_path: str, ext: str) -> str:
        """读取文件内容"""
        if ext == '.txt':
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:  # .docx
            doc = Document(file_path)
            return '\n'.join(paragraph.text for paragraph in doc.paragraphs)
            
    @staticmethod
    def _write_content(file_path: str, content: str, ext: str):
        """写入文件内容"""
        if ext == '.txt':
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        else:  # .docx
            doc = Document()
            for line in content.splitlines():
                doc.add_paragraph(line)
            doc.save(file_path)
            
    @staticmethod
    def _restore_from_map(masked_path: str, map_path: str, save_path: str):
        """从映射表恢复文件"""
        _, ext = os.path.splitext(masked_path)
        
        # 读取映射表
        with open(map_path, 'r', encoding='utf-8') as f:
            mapping_data = json.load(f)
        
        # 兼容旧格式的映射表
        if isinstance(mapping_data, dict) and 'replacements' in mapping_data:
            replacements = mapping_data['replacements']
        else:
            # 旧格式：只有计数器，没有原始文本
            replacements = {}
            counters = mapping_data if isinstance(mapping_data, dict) else {}
            for label, count in counters.items():
                for i in range(1, count + 1):
                    placeholder = f"{label}_{i}"
                    replacements[placeholder] = f"[原始{label}]"  # 用占位符表示
            
        # 读取脱敏文件
        if ext == '.txt':
            with open(masked_path, 'r', encoding='utf-8') as f:
                content = f.read()
        else:
            doc = Document(masked_path)
            content = '\n'.join(paragraph.text for paragraph in doc.paragraphs)
            
        # 恢复内容：用真正的原始文本替换占位符
        for placeholder, original_text in replacements.items():
            content = content.replace(placeholder, original_text)
                
        # 保存恢复文件
        if ext == '.txt':
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(content)
        else:
            new_doc = Document()
            for line in content.splitlines():
                new_doc.add_paragraph(line)
            new_doc.save(save_path)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)
    
    # 设置主题（可选）
    # setTheme(Theme.DARK)  # 深色主题
    # setTheme(Theme.LIGHT)  # 浅色主题
    
    window = MaskToolInterface()
    window.setWindowTitle("公文脱敏助手 - Fluent Design")
    window.setMinimumSize(1000, 700)  # 设置最小尺寸而不是固定尺寸
    window.resize(1200, 800)  # 默认尺寸
    window.setWindowIcon(FluentIcon.DOCUMENT.icon())
    
    # 确保窗口大小策略正确
    window.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()