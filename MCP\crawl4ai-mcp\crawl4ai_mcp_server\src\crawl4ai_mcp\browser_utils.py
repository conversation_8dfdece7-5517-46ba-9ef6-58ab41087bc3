"""Browser configuration utilities for all tools."""

import os
from crawl4ai import BrowserConfig
from .config import config


def create_browser_config(browser_config_override: dict = None) -> BrowserConfig:
    """Create browser configuration with Edge settings for all tools."""
    
    # 设置Edge浏览器环境变量
    if config.browser.browser_executable_path and os.path.exists(config.browser.browser_executable_path):
        os.environ["PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH"] = config.browser.browser_executable_path
    
    # 默认浏览器配置
    default_browser_config = {
        "browser_type": "chromium",
        "headless": config.browser.headless,
        "viewport_width": config.browser.viewport_width,
        "viewport_height": config.browser.viewport_height,
        "use_persistent_context": config.browser.use_persistent_context,
        "user_data_dir": config.browser.user_data_dir,
        "verbose": not config.browser.headless,  # 在非无头模式时启用详细日志
        "extra_args": [
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-web-security",
            "--disable-dev-shm-usage"
        ]
    }
    
    # 用户配置覆盖默认配置
    if browser_config_override:
        merged_config = {**default_browser_config, **browser_config_override}
    else:
        merged_config = default_browser_config
    
    # 确保用户数据目录存在
    if merged_config.get("user_data_dir"):
        os.makedirs(merged_config["user_data_dir"], exist_ok=True)
    
    return BrowserConfig(**merged_config)


def should_show_browser() -> bool:
    """Check if browser should be visible based on configuration."""
    return not config.browser.headless
