"""Configuration management for Crawl4AI MCP Server."""

import os
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class LLMConfig(BaseModel):
    """LLM configuration for content filtering."""
    provider: str = Field(default="openai", description="LLM provider")
    api_token: Optional[str] = Field(default=None, description="API token")
    model: str = Field(default="gpt-3.5-turbo", description="Model name")


class BrowserConfig(BaseModel):
    """Browser configuration for Edge/Chromium."""
    browser_executable_path: Optional[str] = Field(default=None, description="Path to browser executable")
    headless: bool = Field(default=False, description="Run browser in headless mode")
    user_data_dir: Optional[str] = Field(default=None, description="User data directory for persistent sessions")
    use_persistent_context: bool = Field(default=True, description="Enable persistent browser context")
    viewport_width: int = Field(default=1280, description="Browser viewport width")
    viewport_height: int = Field(default=720, description="Browser viewport height")


class ServerConfig(BaseModel):
    """Main server configuration."""
    name: str = Field(default="crawl4ai-mcp", description="Server name")
    version: str = Field(default="0.1.0", description="Server version")
    
    # LLM configuration
    llm: LLMConfig = Field(default_factory=LLMConfig)
    
    # Browser configuration
    browser: BrowserConfig = Field(default_factory=BrowserConfig)
    
    # Default settings
    default_cache_mode: str = Field(default="write_only", description="Default cache mode")
    max_concurrent_crawls: int = Field(default=5, description="Max concurrent crawl operations")


def load_config() -> ServerConfig:
    """Load configuration from environment variables and defaults."""
    
    # Load LLM config from environment
    llm_config = LLMConfig(
        provider=os.getenv("CRAWL4AI_LLM_PROVIDER", "openai"),
        api_token=os.getenv("OPENAI_API_KEY") or os.getenv("ANTHROPIC_API_KEY"),
        model=os.getenv("CRAWL4AI_LLM_MODEL", "gpt-3.5-turbo")
    )
    
    # Load Browser config from environment
    browser_config = BrowserConfig(
        browser_executable_path=os.getenv("CRAWL4AI_BROWSER_PATH", r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe"),
        headless=os.getenv("CRAWL4AI_HEADLESS", "false").lower() == "true",
        user_data_dir=os.getenv("CRAWL4AI_USER_DATA_DIR", os.path.expanduser(r"~\.crawl4ai\edge_profile")),
        use_persistent_context=os.getenv("CRAWL4AI_PERSISTENT_CONTEXT", "true").lower() == "true",
        viewport_width=int(os.getenv("CRAWL4AI_VIEWPORT_WIDTH", "1280")),
        viewport_height=int(os.getenv("CRAWL4AI_VIEWPORT_HEIGHT", "720"))
    )
    
    return ServerConfig(
        name=os.getenv("CRAWL4AI_MCP_NAME", "crawl4ai-mcp"),
        version=os.getenv("CRAWL4AI_MCP_VERSION", "0.1.0"),
        llm=llm_config,
        browser=browser_config,
        default_cache_mode=os.getenv("CRAWL4AI_CACHE_MODE", "write_only"),
        max_concurrent_crawls=int(os.getenv("CRAWL4AI_MAX_CONCURRENT", "5"))
    )


# Global config instance
config = load_config()