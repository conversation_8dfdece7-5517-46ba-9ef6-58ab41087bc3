"""JavaScript execution tool for Crawl4AI MCP Server."""

from typing import Dict, Any, List, Union

import mcp.types as types
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig

from ..utils import create_json_response, create_error_response, validate_url, create_tool_schema
from ..browser_utils import create_browser_config


class JavaScriptTool:
    """Tool for executing JavaScript on web pages."""
    
    @staticmethod
    def get_schema() -> types.Tool:
        """Get the tool schema for MCP."""
        return types.Tool(
            name="execute_js",
            description="Execute JavaScript code on a web page and return the modified page content",
            inputSchema=create_tool_schema(
                properties={
                    "url": {
                        "type": "string",
                        "description": "Absolute http/https URL to execute JavaScript on"
                    },
                    "js_code": {
                        "oneOf": [
                            {"type": "string"},
                            {"type": "array", "items": {"type": "string"}}
                        ],
                        "description": "JavaScript code to execute (string or array of strings)"
                    }
                },
                required=["url", "js_code"]
            )
        )
    
    @staticmethod
    async def execute(arguments: Dict[str, Any]) -> List[types.TextContent]:
        """Execute the JavaScript tool."""
        try:
            # Extract and validate arguments
            url = arguments.get("url")
            if not url:
                return create_error_response("URL is required", 400)
            
            js_code = arguments.get("js_code")
            if not js_code:
                return create_error_response("JavaScript code is required", 400)
            
            # Validate URL
            try:
                validated_url = validate_url(url)
            except Exception as e:
                return create_error_response(f"Invalid URL: {str(e)}", 400)
            
            # Execute JavaScript
            result = await JavaScriptTool._execute_javascript(validated_url, js_code)
            
            return create_json_response(result)
            
        except Exception as e:
            return create_error_response(f"JavaScript execution failed: {str(e)}")
    
    @staticmethod
    async def _execute_javascript(
        url: str,
        js_code: Union[str, List[str]]
    ) -> Dict[str, Any]:
        """Execute JavaScript on the page and return results."""
        
        # Normalize js_code to list
        if isinstance(js_code, str):
            js_scripts = [js_code]
        else:
            js_scripts = js_code
        
        # Configure crawler for JavaScript execution
        crawler_config = CrawlerRunConfig(js_code=js_scripts)
        
        # Create browser config with Edge settings
        browser_cfg = create_browser_config()
        
        async with AsyncWebCrawler(config=browser_cfg) as crawler:
            results = await crawler.arun(url=url, config=crawler_config)
            
            if not results or not results[0].success:
                error_msg = results[0].error_message if results else "JavaScript execution failed"
                raise Exception(error_msg)
            
            # Return the full crawl result as dict
            result_dict = results[0].model_dump()
            
            # Add execution status
            result_dict["js_execution_success"] = True
            result_dict["executed_scripts"] = js_scripts
            
            return result_dict