# 代码风格和约定

## Python代码风格
- 使用**async/await**模式进行异步编程
- **类型注解**：使用typing模块进行完整的类型标注
- **Pydantic模型**：配置和数据验证使用Pydantic BaseModel
- **docstring格式**：使用Google风格的docstring

## 命名约定
- **类名**：PascalCase (如：`BrowserConfig`, `CrawlTool`)
- **函数/方法名**：snake_case (如：`create_browser_config`, `execute_tool`)
- **变量名**：snake_case
- **常量**：UPPER_SNAKE_CASE
- **异步方法**：以`async def`开头，方法名通常不需要`a`前缀

## 目录结构约定
- **工具类**：放在`tools/`目录下，每个工具一个文件
- **配置**：集中在`config.py`文件中
- **工具类命名**：以`Tool`结尾，如`CrawlTool`, `ScreenshotTool`

## 错误处理
- 使用`try/except`块包装可能出错的操作
- 返回标准化的错误响应格式
- 记录详细的错误信息到日志

## 工具实现模式
每个工具类都应该包含：
1. `get_schema()` - 静态方法，返回MCP工具模式
2. `execute()` - 静态异步方法，执行工具逻辑
3. 私有辅助方法以`_`开头

## 配置管理
- 使用环境变量进行配置覆盖
- 提供合理的默认值
- 使用Pydantic进行配置验证